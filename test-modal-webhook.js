// Script para testar o fluxo completo do modal → webhook N8N
// Simula o que acontece quando o modal de solicitação de orçamento é enviado

async function testModalWebhookFlow() {
  console.log('🧪 TESTE: Fluxo Modal → Webhook N8N');
  console.log('=' .repeat(60));
  
  // Dados que o modal enviaria (sem email)
  const modalData = {
    lead: {
      nome: '<PERSON>',
      telefone: '61999887766',
      email: null, // Campo removido
      empresa: 'Mercadinho da Maria',
      aceite_termos: true,
      aceite_whatsapp: true
    },
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Test Browser)'
  };

  console.log('📋 Dados do modal (simulado):');
  console.log(JSON.stringify(modalData, null, 2));
  console.log('');

  // URL da Edge Function capture-lead
  const captureLeadUrl = 'https://your-project.supabase.co/functions/v1/capture-lead';
  
  console.log('🎯 Testando Edge Function capture-lead...');
  console.log('URL:', captureLeadUrl);
  console.log('');

  try {
    const response = await fetch(captureLeadUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_ANON_KEY'
      },
      body: JSON.stringify(modalData)
    });

    console.log('📊 Status da resposta:', response.status);
    console.log('📊 Status text:', response.statusText);
    
    const responseData = await response.json();
    console.log('📋 Resposta da Edge Function:');
    console.log(JSON.stringify(responseData, null, 2));
    
    if (response.ok && responseData.success) {
      console.log('\n✅ SUCESSO! Modal → capture-lead funcionando!');
      console.log('🔄 A automação deve ter enviado para N8N automaticamente');
      console.log('📧 Verifique se chegou email e se o lead apareceu no N8N');
    } else {
      console.log('\n❌ Erro na Edge Function capture-lead');
      console.log('🔍 Verifique os logs da Edge Function');
    }
    
  } catch (error) {
    console.error('❌ Erro ao conectar com capture-lead:', error.message);
    console.log('\n🔧 Possíveis causas:');
    console.log('- URL da Edge Function incorreta');
    console.log('- Token de autorização inválido');
    console.log('- Edge Function não deployada');
  }
}

// Instruções para uso
console.log('📝 INSTRUÇÕES PARA TESTE:');
console.log('1. Substitua YOUR_PROJECT e YOUR_ANON_KEY pelos valores reais');
console.log('2. Execute: node test-modal-webhook.js');
console.log('3. Verifique se o lead chegou no N8N');
console.log('4. Verifique os logs das Edge Functions no Supabase');
console.log('');

// Descomente a linha abaixo para executar o teste
// testModalWebhookFlow();
