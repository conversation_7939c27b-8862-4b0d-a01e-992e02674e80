// Script detalhado para testar webhook N8N
async function testWebhook() {
  console.log('🔍 Teste Detalhado do Webhook N8N\n');
  console.log('=' .repeat(50));
  
  // URLs para testar
  const urls = [
    'https://assismax.app.n8n.cloud/webhook-test/assismax',
    'https://assismax.app.n8n.cloud/webhook/assismax'
  ];
  
  const testData = {
    nome: '<PERSON>',
    telefone: '(61) 98888-7777',
    origem: 'chatbot',
    data: new Date().toISOString(),
    status: 'novo'
  };

  console.log('📋 Dados de teste (SEM EMAIL):', JSON.stringify(testData, null, 2));
  console.log('=' .repeat(50));
  
  for (const url of urls) {
    console.log(`\n🎯 Testando: ${url}`);
    console.log('-' .repeat(50));
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      console.log('📊 Status:', response.status, response.statusText);
      
      const responseText = await response.text();
      if (responseText) {
        try {
          const responseJson = JSON.parse(responseText);
          console.log('📋 Resposta JSON:', JSON.stringify(responseJson, null, 2));
        } catch {
          console.log('📋 Resposta Texto:', responseText);
        }
      }
      
      if (response.ok) {
        console.log('✅ SUCESSO! Esta URL está funcionando.');
        console.log('\n💡 Use esta URL no seu código:');
        console.log(`   ${url}`);
        break;
      } else {
        console.log('❌ Esta URL não está funcionando.');
      }
      
    } catch (error) {
      console.error('❌ Erro de conexão:', error.message);
    }
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📌 IMPORTANTE:');
  console.log('- Certifique-se que o workflow está ATIVO no N8N');
  console.log('- O webhook só funciona com workflow ativo');
  console.log('- Verifique se o path está correto no N8N');
  console.log('- O sistema agora funciona SEM EMAIL!');
}

testWebhook();