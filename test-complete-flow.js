// Teste completo do fluxo sem email
const testCompleteFlow = async () => {
  console.log('🚀 TESTE COMPLETO DO FLUXO SEM EMAIL\n');
  console.log('=' .repeat(60));
  
  // Simular dados como viriam do chatbot
  const leadData = {
    nome: '<PERSON>',
    telefone: '61987654321', // Sem formatação, como vem do chatbot
    origem: 'chatbot',
    data: new Date().toISOString(),
    status: 'novo'
  };
  
  console.log('1️⃣ DADOS ORIGINAIS DO CHATBOT:');
  console.log(JSON.stringify(leadData, null, 2));
  console.log('\n' + '-'.repeat(60) + '\n');
  
  // Formatar telefone
  const telefoneFormatado = leadData.telefone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  const dadosFormatados = {
    ...leadData,
    telefone: telefoneFormatado
  };
  
  console.log('2️⃣ DADOS FORMATADOS PARA ENVIO:');
  console.log(JSON.stringify(dadosFormatados, null, 2));
  console.log('\n' + '-'.repeat(60) + '\n');
  
  // Enviar para webhook
  console.log('3️⃣ ENVIANDO PARA WEBHOOK N8N...\n');
  
  try {
    const response = await fetch('https://assismax.app.n8n.cloud/webhook/assismax', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(dadosFormatados)
    });
    
    console.log('📊 Status:', response.status, response.statusText);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Resposta:', JSON.stringify(result, null, 2));
      
      console.log('\n' + '=' .repeat(60));
      console.log('🎉 FLUXO COMPLETO FUNCIONANDO!');
      console.log('\n📋 RESUMO DO QUE ACONTECEU:');
      console.log('✓ Lead capturado SEM EMAIL');
      console.log('✓ Telefone formatado corretamente');
      console.log('✓ Webhook N8N processou com sucesso');
      console.log('✓ Dados devem estar no Google Sheets');
      console.log('✓ Dados devem estar no Supabase');
      console.log('✓ Email de notificação enviado (se OAuth reconectado)');
      console.log('\n🚀 Sistema pronto para produção!');
    } else {
      console.log('❌ Erro:', response.statusText);
    }
    
  } catch (error) {
    console.error('❌ Erro de conexão:', error.message);
  }
  
  console.log('\n' + '=' .repeat(60));
};

// Executar teste
testCompleteFlow();