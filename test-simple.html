<!DOCTYPE html>
<html>
<head>
    <title>Teste Formulário Sem Email</title>
</head>
<body>
    <h1>Teste do Formulário ASSISMAX</h1>
    
    <form id="leadForm">
        <label>Nome:</label><br>
        <input type="text" id="nome" value="Ana Silva"><br><br>
        
        <label>Telefone:</label><br>
        <input type="tel" id="telefone" value="(61) 98765-4321"><br><br>
        
        <label>Empresa (opcional):</label><br>
        <input type="text" id="empresa" value="Padaria da Ana"><br><br>
        
        <label>
            <input type="checkbox" id="aceite_termos" checked> Aceito os termos
        </label><br>
        
        <label>
            <input type="checkbox" id="aceite_whatsapp" checked> Aceito contato via WhatsApp
        </label><br><br>
        
        <button type="submit">Enviar Lead</button>
    </form>
    
    <div id="resultado"></div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        const supabase = window.supabase.createClient(
            'https://rsydniuoipecgsocsuim.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzeWRuaXVvaXBlY2dzb2NzdWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM2MDY1NzgsImV4cCI6MjA2OTE4MjU3OH0.l3eDu9nwFzIXI4Hj9I3Wf3ZcKRF6gvxGQg-6Cx4a1oA'
        );

        document.getElementById('leadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const resultado = document.getElementById('resultado');
            resultado.innerHTML = '⏳ Enviando...';
            
            const formData = {
                nome: document.getElementById('nome').value,
                telefone: document.getElementById('telefone').value,
                empresa: document.getElementById('empresa').value || null,
                aceite_termos: document.getElementById('aceite_termos').checked,
                aceite_whatsapp: document.getElementById('aceite_whatsapp').checked
            };
            
            console.log('Enviando:', formData);
            
            try {
                const { data, error } = await supabase.functions.invoke('capture-lead', {
                    body: {
                        lead: {
                            ...formData,
                            email: null
                        },
                        ip_address: '127.0.0.1',
                        user_agent: navigator.userAgent
                    }
                });
                
                if (error) {
                    console.error('Erro:', error);
                    resultado.innerHTML = `❌ Erro: ${error.message}`;
                } else {
                    console.log('Sucesso:', data);
                    resultado.innerHTML = `✅ Sucesso! Lead capturado sem email.`;
                }
                
            } catch (err) {
                console.error('Erro geral:', err);
                resultado.innerHTML = `❌ Erro geral: ${err.message}`;
            }
        });
    </script>
</body>
</html>