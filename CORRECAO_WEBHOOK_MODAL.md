# 🔧 CORREÇÃO: Modal de Orçamento → Webhook N8N

## 📋 **Problema Identificado**

O modal de solicitação de orçamento não estava enviando dados para o webhook N8N, enquanto o chatbot funcionava corretamente.

### **Causa <PERSON>z**
A Edge Function `lead-automation` não incluía envio para N8N no fluxo normal de automação.

## ✅ **Alterações Realizadas**

### 1. **Edge Function lead-automation** (`supabase/functions/lead-automation/index.ts`)

**Adicionado:**
- Nova função `sendToN8N()` (linhas 356-381)
- Chamada para N8N no fluxo principal (linha 161-167)
- Atualização da interface `N8NLeadPayload` para aceitar `email: null`
- Inclusão de 'n8n_webhook' nos steps executados

**Função sendToN8N:**
```typescript
async function sendToN8N(leadData: any) {
  const webhookUrl = Deno.env.get('N8N_WEBHOOK_URL') || 'https://assismax.app.n8n.cloud/webhook/assismax';
  
  const payload = {
    empresa_id: '231f795a-b14c-438b-a896-2f2e479cfa02',
    nome: leadData.nome,
    telefone: leadData.telefone,
    email: leadData.email || null,
    origem: leadData.origem || 'landing_page',
    data: leadData.created_at || new Date().toISOString(),
    status: leadData.status || 'novo'
  };

  const response = await fetch(webhookUrl, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    throw new Error(`N8N webhook retornou status ${response.status}`);
  }
}
```

### 2. **Variáveis de Ambiente**

**✅ Já configurado:** A variável `VITE_N8N_WEBHOOK_URL` já existe no `.env`
- Edge Function usa URL hardcoded para evitar dependência de variáveis de ambiente

### 3. **Script de Teste** (`test-modal-webhook.js`)

Criado script para testar o fluxo completo do modal.

## 🚀 **Próximos Passos**

### 1. **Deploy da Edge Function**
```bash
supabase functions deploy lead-automation
```

### 2. **✅ Variáveis já Configuradas**
A URL do webhook já está configurada no código da Edge Function.

### 3. **Testar o Fluxo**
1. Abrir o modal de solicitação de orçamento
2. Preencher dados (sem email)
3. Verificar se chegou no N8N
4. Verificar logs das Edge Functions

### 4. **Verificar N8N**
- Confirmar que o workflow está ativo
- Verificar se aceita `email: null`
- Testar com dados reais

## 📊 **Fluxo Corrigido**

```
Modal de Orçamento
    ↓
useLeadCapture
    ↓
Edge Function: capture-lead
    ↓
Edge Function: lead-automation
    ↓
✅ N8N Webhook (NOVO!)
    ↓
Email + Google Sheets + WhatsApp
```

## 🔍 **Logs para Monitorar**

### Edge Function capture-lead:
```
✅ Lead salvo no Supabase: [lead_id]
```

### Edge Function lead-automation:
```
📤 Enviando para N8N: https://assismax.app.n8n.cloud/webhook/assismax
📋 Payload: {...}
✅ Dados enviados para N8N com sucesso
```

### N8N:
- Verificar execuções do workflow
- Confirmar recebimento dos dados
- Verificar se email chegou

## ⚠️ **Pontos de Atenção**

1. **Campo Email**: Agora é `null` - verificar se N8N aceita
2. **Rate Limiting**: Monitorar se N8N tem limites de requisições
3. **Error Handling**: Falhas no N8N não devem quebrar o fluxo principal
4. **Logs**: Monitorar logs para identificar problemas

## 🧪 **Como Testar**

1. **Teste Manual:**
   - Usar o modal na landing page
   - Verificar se lead aparece no N8N

2. **Teste com Script:**
   ```bash
   node test-modal-webhook.js
   ```

3. **Verificar Logs:**
   ```bash
   supabase functions logs lead-automation
   ```

## ✅ **Resultado Esperado**

Após as correções, tanto o **modal de orçamento** quanto o **chatbot** devem enviar leads para o N8N automaticamente, mantendo a consistência no fluxo de captação.
