// Teste de Produção - Sistema ASSISMAX
const testProduction = async () => {
  console.log('🚀 TESTE DE PRODUÇÃO - ASSISMAX\n');
  console.log('=' .repeat(60));
  console.log('📅 Data/Hora:', new Date().toLocaleString('pt-BR'));
  console.log('🔗 URL Produção:', 'https://assismax.app.n8n.cloud/webhook/assismax');
  console.log('=' .repeat(60));
  
  // Dados de teste realistas
  const testeLead = {
    nome: '<PERSON><PERSON>',
    telefone: '(61) 99234-5678',
    origem: 'chatbot',
    data: new Date().toISOString(),
    status: 'novo'
  };
  
  console.log('\n📋 DADOS DO LEAD (SEM EMAIL):');
  console.log(JSON.stringify(testeLead, null, 2));
  console.log('\n' + '-'.repeat(60));
  
  console.log('\n⏳ Enviando para webhook de produção...\n');
  
  try {
    const inicio = Date.now();
    
    const response = await fetch('https://assismax.app.n8n.cloud/webhook/assismax', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testeLead)
    });
    
    const tempoResposta = Date.now() - inicio;
    
    console.log('📊 STATUS:', response.status, response.statusText);
    console.log('⏱️  Tempo de resposta:', tempoResposta, 'ms');
    
    const responseText = await response.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
      console.log('📨 Resposta:', JSON.stringify(responseData, null, 2));
    } catch {
      console.log('📨 Resposta (texto):', responseText);
    }
    
    console.log('\n' + '=' .repeat(60));
    
    if (response.ok) {
      console.log('✅ SUCESSO - SISTEMA EM PRODUÇÃO FUNCIONANDO!');
      console.log('\n🎯 CHECKLIST DE PRODUÇÃO:');
      console.log('✓ Webhook respondendo corretamente');
      console.log('✓ Lead processado sem email');
      console.log('✓ Tempo de resposta aceitável:', tempoResposta + 'ms');
      console.log('\n📍 PRÓXIMOS PASSOS:');
      console.log('1. Verificar se o lead chegou no Google Sheets');
      console.log('2. Verificar se o lead chegou no Supabase');
      console.log('3. Verificar se o email de notificação foi enviado');
      console.log('\n💡 IMPORTANTE:');
      console.log('- Sistema está capturando leads apenas com NOME e TELEFONE');
      console.log('- Email foi removido para facilitar cadastro de donas de casa');
      console.log('- Foco 100% no WhatsApp para comunicação');
    } else {
      console.log('❌ ERRO NO WEBHOOK DE PRODUÇÃO');
      console.log('\n🔧 TROUBLESHOOTING:');
      console.log('1. Verifique se o workflow está ATIVO no N8N');
      console.log('2. Confirme se a URL está correta');
      console.log('3. Verifique os logs de execução no N8N');
    }
    
  } catch (error) {
    console.error('❌ ERRO DE CONEXÃO:', error.message);
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('1. Verifique sua conexão com a internet');
    console.log('2. Confirme se o N8N está online');
    console.log('3. Verifique se há algum firewall bloqueando');
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('📝 Log salvo em:', new Date().toLocaleString('pt-BR'));
  console.log('=' .repeat(60));
};

// Executar teste
console.clear();
testProduction();