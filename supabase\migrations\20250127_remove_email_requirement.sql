-- =====================================================
-- MIGRATION: Tornar email opcional para leads
-- Data: 2025-01-27
-- Descrição: Remove obrigatoriedade de email para facilitar
-- cadastro de donas de casa que não usam email
-- =====================================================

-- 1. Tornar email opcional na tabela leads
ALTER TABLE leads 
ALTER COLUMN email DROP NOT NULL,
ALTER COLUMN email SET DEFAULT NULL;

-- 2. Remover índice de email (não será mais usado para busca)
DROP INDEX IF EXISTS idx_leads_email;

-- 3. Atualizar comentário da coluna
COMMENT ON COLUMN leads.email IS 'Email do lead (opcional) - removido dos formulários para simplificar cadastro';

-- 4. Criar função para verificar duplicata apenas por telefone
CREATE OR REPLACE FUNCTION check_lead_duplicate_by_phone(
    p_telefone VARCHAR(20),
    p_empresa_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM leads 
        WHERE telefone = p_telefone 
        AND empresa_id = p_empresa_id
        AND status NOT IN ('perdido', 'cancelado')
    );
END;
$$ LANGUAGE plpgsql;

-- 5. Adicionar comentário explicativo
COMMENT ON FUNCTION check_lead_duplicate_by_phone IS 'Verifica se já existe um lead ativo com o mesmo telefone na empresa';

-- =====================================================
-- NOTAS:
-- - Email permanece obrigatório em funcionarios (auth)
-- - Leads existentes mantêm seus emails
-- - Sistema preparado para email opcional
-- - Foco 100% em WhatsApp para comunicação
-- =====================================================