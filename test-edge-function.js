// Teste específico da Edge Function capture-lead
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://rsydniuoipecgsocsuim.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzeWRuaXVvaXBlY2dzb2NzdWltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM2MDY1NzgsImV4cCI6MjA2OTE4MjU3OH0.l3eDu9nwFzIXI4Hj9I3Wf3ZcKRF6gvxGQg-6Cx4a1oA';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const testEdgeFunction = async () => {
  console.log('🧪 TESTE DA EDGE FUNCTION CAPTURE-LEAD\n');
  console.log('=' .repeat(50));
  
  const testData = {
    lead: {
      nome: 'Maria das Graças',
      telefone: '(61) 99876-5432',
      email: null,
      empresa: 'Casa da Maria',
      aceite_termos: true,
      aceite_whatsapp: true
    },
    ip_address: '***********',
    user_agent: 'Test Browser'
  };
  
  console.log('📋 Dados enviados:', JSON.stringify(testData, null, 2));
  console.log('\n⏳ Chamando edge function...\n');
  
  try {
    const { data, error } = await supabase.functions.invoke('capture-lead', {
      body: testData
    });
    
    if (error) {
      console.error('❌ Erro na Edge Function:', error);
      console.error('   Message:', error.message);
      console.error('   Details:', error.context || 'N/A');
      return;
    }
    
    console.log('✅ Sucesso!');
    console.log('📨 Resposta:', JSON.stringify(data, null, 2));
    
  } catch (err) {
    console.error('❌ Erro geral:', err.message);
  }
  
  console.log('\n' + '=' .repeat(50));
};

testEdgeFunction();